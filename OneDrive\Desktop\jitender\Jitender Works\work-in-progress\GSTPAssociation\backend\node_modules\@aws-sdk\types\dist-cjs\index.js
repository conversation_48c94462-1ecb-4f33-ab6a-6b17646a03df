"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  AbortController: () => import_types.AbortController,
  AbortHandler: () => import_types.AbortHandler,
  AbortSignal: () => import_types.AbortSignal,
  AbsoluteLocation: () => import_types.AbsoluteLocation,
  AuthScheme: () => import_types.AuthScheme,
  AvailableMessage: () => import_types.AvailableMessage,
  AvailableMessages: () => import_types.AvailableMessages,
  AwsCredentialIdentity: () => import_types.AwsCredentialIdentity,
  AwsCredentialIdentityProvider: () => import_types.AwsCredentialIdentityProvider,
  BinaryHeaderValue: () => import_types.BinaryHeaderValue,
  BlobTypes: () => import_types.BlobTypes,
  BodyLengthCalculator: () => import_types.BodyLengthCalculator,
  BooleanHeaderValue: () => import_types.BooleanHeaderValue,
  BuildHandler: () => import_types.BuildHandler,
  BuildHandlerArguments: () => import_types.BuildHandlerArguments,
  BuildHandlerOptions: () => import_types.BuildHandlerOptions,
  BuildHandlerOutput: () => import_types.BuildHandlerOutput,
  BuildMiddleware: () => import_types.BuildMiddleware,
  ByteHeaderValue: () => import_types.ByteHeaderValue,
  Checksum: () => import_types.Checksum,
  ChecksumConstructor: () => import_types.ChecksumConstructor,
  Client: () => import_types.Client,
  Command: () => import_types.Command,
  ConnectConfiguration: () => import_types.ConnectConfiguration,
  ConnectionManager: () => import_types.ConnectionManager,
  ConnectionManagerConfiguration: () => import_types.ConnectionManagerConfiguration,
  ConnectionPool: () => import_types.ConnectionPool,
  DateInput: () => import_types.DateInput,
  Decoder: () => import_types.Decoder,
  DeserializeHandler: () => import_types.DeserializeHandler,
  DeserializeHandlerArguments: () => import_types.DeserializeHandlerArguments,
  DeserializeHandlerOptions: () => import_types.DeserializeHandlerOptions,
  DeserializeHandlerOutput: () => import_types.DeserializeHandlerOutput,
  DeserializeMiddleware: () => import_types.DeserializeMiddleware,
  DocumentType: () => import_types.DocumentType,
  Encoder: () => import_types.Encoder,
  Endpoint: () => import_types.Endpoint,
  EndpointARN: () => import_types.EndpointARN,
  EndpointBearer: () => import_types.EndpointBearer,
  EndpointObjectProperty: () => import_types.EndpointObjectProperty,
  EndpointParameters: () => import_types.EndpointParameters,
  EndpointPartition: () => import_types.EndpointPartition,
  EndpointURL: () => import_types.EndpointURL,
  EndpointURLScheme: () => import_types.EndpointURLScheme,
  EndpointV2: () => import_types.EndpointV2,
  EventSigner: () => import_types.EventSigner,
  EventSigningArguments: () => import_types.EventSigningArguments,
  EventStreamMarshaller: () => import_types.EventStreamMarshaller,
  EventStreamMarshallerDeserFn: () => import_types.EventStreamMarshallerDeserFn,
  EventStreamMarshallerSerFn: () => import_types.EventStreamMarshallerSerFn,
  EventStreamPayloadHandler: () => import_types.EventStreamPayloadHandler,
  EventStreamPayloadHandlerProvider: () => import_types.EventStreamPayloadHandlerProvider,
  EventStreamRequestSigner: () => import_types.EventStreamRequestSigner,
  EventStreamSerdeContext: () => import_types.EventStreamSerdeContext,
  EventStreamSerdeProvider: () => import_types.EventStreamSerdeProvider,
  EventStreamSignerProvider: () => import_types.EventStreamSignerProvider,
  ExponentialBackoffJitterType: () => import_types.ExponentialBackoffJitterType,
  ExponentialBackoffStrategyOptions: () => import_types.ExponentialBackoffStrategyOptions,
  FinalizeHandler: () => import_types.FinalizeHandler,
  FinalizeHandlerArguments: () => import_types.FinalizeHandlerArguments,
  FinalizeHandlerOutput: () => import_types.FinalizeHandlerOutput,
  FinalizeRequestHandlerOptions: () => import_types.FinalizeRequestHandlerOptions,
  FinalizeRequestMiddleware: () => import_types.FinalizeRequestMiddleware,
  FormattedEvent: () => import_types.FormattedEvent,
  GetAwsChunkedEncodingStream: () => import_types.GetAwsChunkedEncodingStream,
  GetAwsChunkedEncodingStreamOptions: () => import_types.GetAwsChunkedEncodingStreamOptions,
  Handler: () => import_types.Handler,
  HandlerExecutionContext: () => import_types.HandlerExecutionContext,
  HandlerOptions: () => import_types.HandlerOptions,
  Hash: () => import_types.Hash,
  HashConstructor: () => import_types.HashConstructor,
  HeaderBag: () => import_types.HeaderBag,
  HostAddressType: () => HostAddressType,
  HttpAuthDefinition: () => import_types.HttpAuthDefinition,
  HttpAuthLocation: () => import_types.HttpAuthLocation,
  HttpHandlerOptions: () => import_types.HttpHandlerOptions,
  HttpMessage: () => import_types.HttpMessage,
  HttpRequest: () => import_types.HttpRequest,
  HttpResponse: () => import_types.HttpResponse,
  Identity: () => import_types.Identity,
  IdentityProvider: () => import_types.IdentityProvider,
  IniSection: () => import_types.IniSection,
  InitializeHandler: () => import_types.InitializeHandler,
  InitializeHandlerArguments: () => import_types.InitializeHandlerArguments,
  InitializeHandlerOptions: () => import_types.InitializeHandlerOptions,
  InitializeHandlerOutput: () => import_types.InitializeHandlerOutput,
  InitializeMiddleware: () => import_types.InitializeMiddleware,
  Int64: () => import_types.Int64,
  IntegerHeaderValue: () => import_types.IntegerHeaderValue,
  LongHeaderValue: () => import_types.LongHeaderValue,
  MemoizedProvider: () => import_types.MemoizedProvider,
  Message: () => import_types.Message,
  MessageDecoder: () => import_types.MessageDecoder,
  MessageEncoder: () => import_types.MessageEncoder,
  MessageHeaderValue: () => import_types.MessageHeaderValue,
  MessageHeaders: () => import_types.MessageHeaders,
  MessageSigner: () => import_types.MessageSigner,
  MetadataBearer: () => import_types.MetadataBearer,
  MiddlewareStack: () => import_types.MiddlewareStack,
  MiddlewareType: () => import_types.MiddlewareType,
  PaginationConfiguration: () => import_types.PaginationConfiguration,
  Paginator: () => import_types.Paginator,
  ParsedIniData: () => import_types.ParsedIniData,
  Pluggable: () => import_types.Pluggable,
  Priority: () => import_types.Priority,
  Profile: () => import_types.Profile,
  Provider: () => import_types.Provider,
  QueryParameterBag: () => import_types.QueryParameterBag,
  RegionInfo: () => import_types.RegionInfo,
  RegionInfoProvider: () => import_types.RegionInfoProvider,
  RegionInfoProviderOptions: () => import_types.RegionInfoProviderOptions,
  Relation: () => import_types.Relation,
  RelativeLocation: () => import_types.RelativeLocation,
  RelativeMiddlewareOptions: () => import_types.RelativeMiddlewareOptions,
  RequestContext: () => import_types.RequestContext,
  RequestHandler: () => import_types.RequestHandler,
  RequestHandlerMetadata: () => import_types.RequestHandlerMetadata,
  RequestHandlerOutput: () => import_types.RequestHandlerOutput,
  RequestHandlerProtocol: () => import_types.RequestHandlerProtocol,
  RequestPresigner: () => import_types.RequestPresigner,
  RequestPresigningArguments: () => import_types.RequestPresigningArguments,
  RequestSerializer: () => import_types.RequestSerializer,
  RequestSigner: () => import_types.RequestSigner,
  RequestSigningArguments: () => import_types.RequestSigningArguments,
  ResponseDeserializer: () => import_types.ResponseDeserializer,
  ResponseMetadata: () => import_types.ResponseMetadata,
  RetryBackoffStrategy: () => import_types.RetryBackoffStrategy,
  RetryErrorInfo: () => import_types.RetryErrorInfo,
  RetryErrorType: () => import_types.RetryErrorType,
  RetryStrategy: () => import_types.RetryStrategy,
  RetryStrategyOptions: () => import_types.RetryStrategyOptions,
  RetryStrategyV2: () => import_types.RetryStrategyV2,
  RetryToken: () => import_types.RetryToken,
  RetryableTrait: () => import_types.RetryableTrait,
  SdkError: () => import_types.SdkError,
  SdkStream: () => import_types.SdkStream,
  SdkStreamMixin: () => import_types.SdkStreamMixin,
  SdkStreamMixinInjector: () => import_types.SdkStreamMixinInjector,
  SdkStreamSerdeContext: () => import_types.SdkStreamSerdeContext,
  SerdeContext: () => import_types.SerdeContext,
  SerializeHandler: () => import_types.SerializeHandler,
  SerializeHandlerArguments: () => import_types.SerializeHandlerArguments,
  SerializeHandlerOptions: () => import_types.SerializeHandlerOptions,
  SerializeHandlerOutput: () => import_types.SerializeHandlerOutput,
  SerializeMiddleware: () => import_types.SerializeMiddleware,
  SharedConfigFiles: () => import_types.SharedConfigFiles,
  ShortHeaderValue: () => import_types.ShortHeaderValue,
  SignableMessage: () => import_types.SignableMessage,
  SignedMessage: () => import_types.SignedMessage,
  SigningArguments: () => import_types.SigningArguments,
  SmithyException: () => import_types.SmithyException,
  SourceData: () => import_types.SourceData,
  StandardRetryBackoffStrategy: () => import_types.StandardRetryBackoffStrategy,
  StandardRetryToken: () => import_types.StandardRetryToken,
  Step: () => import_types.Step,
  StreamCollector: () => import_types.StreamCollector,
  StreamHasher: () => import_types.StreamHasher,
  StringHeaderValue: () => import_types.StringHeaderValue,
  StringSigner: () => import_types.StringSigner,
  Terminalware: () => import_types.Terminalware,
  TimestampHeaderValue: () => import_types.TimestampHeaderValue,
  TokenIdentity: () => import_types.TokenIdentity,
  TokenIdentityProvider: () => import_types.TokenIdentityProvider,
  URI: () => import_types.URI,
  UrlParser: () => import_types.UrlParser,
  UserAgent: () => import_types.UserAgent,
  UserAgentPair: () => import_types.UserAgentPair,
  UuidHeaderValue: () => import_types.UuidHeaderValue,
  WaiterConfiguration: () => import_types.WaiterConfiguration,
  WithSdkStreamMixin: () => import_types.WithSdkStreamMixin,
  randomValues: () => import_types.randomValues
});
module.exports = __toCommonJS(src_exports);

// src/abort.ts
var import_types = require("@smithy/types");

// src/auth.ts


// src/blob/blob-types.ts


// src/checksum.ts


// src/client.ts


// src/command.ts


// src/connection.ts


// src/crypto.ts


// src/dns.ts
var HostAddressType = /* @__PURE__ */ ((HostAddressType2) => {
  HostAddressType2["AAAA"] = "AAAA";
  HostAddressType2["A"] = "A";
  return HostAddressType2;
})(HostAddressType || {});

// src/encode.ts


// src/endpoint.ts


// src/eventStream.ts


// src/http.ts


// src/identity/AwsCredentialIdentity.ts


// src/identity/Identity.ts


// src/identity/TokenIdentity.ts


// src/middleware.ts


// src/pagination.ts


// src/profile.ts


// src/response.ts


// src/retry.ts


// src/serde.ts


// src/shapes.ts


// src/signature.ts


// src/stream.ts


// src/transfer.ts


// src/uri.ts


// src/util.ts


// src/waiter.ts

// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  HttpAuthLocation,
  HostAddressType,
  EndpointURLScheme,
  RequestHandlerProtocol
});

