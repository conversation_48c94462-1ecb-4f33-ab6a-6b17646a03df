{"name": "@aws-sdk/middleware-logger", "version": "3.609.0", "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'", "build:cjs": "node ../../scripts/compilation/inline middleware-logger", "build:es": "tsc -p tsconfig.es.json", "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build", "build:types": "tsc -p tsconfig.types.json", "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo", "test": "jest --passWithNoTests", "test:integration": "jest -c jest.config.integ.js"}, "author": {"name": "AWS SDK for JavaScript Team", "email": "", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "types": "./dist-types/index.d.ts", "dependencies": {"@aws-sdk/types": "3.609.0", "@smithy/types": "^3.3.0", "tslib": "^2.6.2"}, "devDependencies": {"@tsconfig/recommended": "1.0.1", "@types/node": "^16.18.96", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typescript": "~4.9.5"}, "engines": {"node": ">=16.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "homepage": "https://github.com/aws/aws-sdk-js-v3/tree/main/packages/middleware-logger", "repository": {"type": "git", "url": "https://github.com/aws/aws-sdk-js-v3.git", "directory": "packages/middleware-logger"}}