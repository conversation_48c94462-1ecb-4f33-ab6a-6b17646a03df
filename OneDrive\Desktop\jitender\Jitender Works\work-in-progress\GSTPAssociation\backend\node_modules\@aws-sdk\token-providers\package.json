{"name": "@aws-sdk/token-providers", "version": "3.614.0", "description": "A collection of token providers", "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "sideEffects": false, "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'", "build:cjs": "node ../../scripts/compilation/inline token-providers", "build:es": "tsc -p tsconfig.es.json", "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build", "build:types": "tsc -p tsconfig.types.json", "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo", "extract:docs": "api-extractor run --local", "test": "jest"}, "keywords": ["aws", "token"], "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "dependencies": {"@aws-sdk/types": "3.609.0", "@smithy/property-provider": "^3.1.3", "@smithy/shared-ini-file-loader": "^3.1.4", "@smithy/types": "^3.3.0", "tslib": "^2.6.2"}, "devDependencies": {"@tsconfig/recommended": "1.0.1", "@types/node": "^16.18.96", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typescript": "~4.9.5"}, "peerDependencies": {"@aws-sdk/client-sso-oidc": "^3.614.0"}, "types": "./dist-types/index.d.ts", "engines": {"node": ">=16.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "browser": {}, "react-native": {}, "homepage": "https://github.com/aws/aws-sdk-js-v3/tree/main/packages/token-providers", "repository": {"type": "git", "url": "https://github.com/aws/aws-sdk-js-v3.git", "directory": "packages/token-providers"}}