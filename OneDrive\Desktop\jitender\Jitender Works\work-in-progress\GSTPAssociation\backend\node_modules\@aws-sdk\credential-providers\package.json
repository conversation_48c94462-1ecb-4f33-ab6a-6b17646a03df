{"name": "@aws-sdk/credential-providers", "version": "3.645.0", "description": "A collection of credential providers, without requiring service clients like STS, Cognito", "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "browser": "./dist-es/index.browser.js", "react-native": "./dist-es/index.browser.js", "sideEffects": false, "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'", "build:cjs": "node ../../scripts/compilation/inline credential-providers", "build:es": "tsc -p tsconfig.es.json", "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build", "build:types": "tsc -p tsconfig.types.json", "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo", "extract:docs": "api-extractor run --local", "test": "jest", "test:integration": "jest -c jest.config.integ.js"}, "keywords": ["aws", "credentials"], "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "dependencies": {"@aws-sdk/client-cognito-identity": "3.645.0", "@aws-sdk/client-sso": "3.645.0", "@aws-sdk/client-sts": "3.645.0", "@aws-sdk/credential-provider-cognito-identity": "3.645.0", "@aws-sdk/credential-provider-env": "3.620.1", "@aws-sdk/credential-provider-http": "3.635.0", "@aws-sdk/credential-provider-ini": "3.645.0", "@aws-sdk/credential-provider-node": "3.645.0", "@aws-sdk/credential-provider-process": "3.620.1", "@aws-sdk/credential-provider-sso": "3.645.0", "@aws-sdk/credential-provider-web-identity": "3.621.0", "@aws-sdk/types": "3.609.0", "@smithy/credential-provider-imds": "^3.2.0", "@smithy/property-provider": "^3.1.3", "@smithy/types": "^3.3.0", "tslib": "^2.6.2"}, "devDependencies": {"@tsconfig/recommended": "1.0.1", "@types/node": "^16.18.96", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typescript": "~4.9.5"}, "types": "./dist-types/index.d.ts", "engines": {"node": ">=16.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "homepage": "https://github.com/aws/aws-sdk-js-v3/tree/main/packages/credential-providers", "repository": {"type": "git", "url": "https://github.com/aws/aws-sdk-js-v3.git", "directory": "packages/credential-providers"}}